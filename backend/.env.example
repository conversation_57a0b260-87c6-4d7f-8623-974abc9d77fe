# Flask配置
FLASK_ENV=development
SECRET_KEY=your-secret-key-here
DEBUG=True

# 服务器配置
HOST=0.0.0.0
PORT=32982

# CORS配置
CORS_ORIGINS=http://localhost:23876

# MySQL数据库配置（旧版本）
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=
DB_NAME=translator
DB_POOL_SIZE=10

# PostgreSQL数据库配置（新版本）
PG_HOST=localhost
PG_PORT=5432
PG_USER=postgres
PG_PASSWORD=your_postgres_password
PG_DATABASE=petkit_translator
PG_POOL_SIZE=10

# 通义千问大模型配置
QWEN_API_KEY=your-qwen-api-key-here
QWEN_MODEL_NAME=qwen-turbo
QWEN_TEMPERATURE=0.1
QWEN_MAX_TOKENS=2000