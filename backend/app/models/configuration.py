"""
配置模型
"""

from typing import Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field


class Configuration(BaseModel):
    """配置模型"""

    id: Optional[int] = Field(None, description="主键ID")
    key: str = Field(..., description="配置键名")
    value: Optional[str] = Field(None, description="配置值")
    create_time: Optional[datetime] = Field(None, description="创建时间")
    update_time: Optional[datetime] = Field(None, description="更新时间")

    class Config:
        from_attributes = True


class ConfigurationCreate(BaseModel):
    """创建配置请求"""

    key: str = Field(..., description="配置键名")
    value: Optional[str] = Field(None, description="配置值")


class ConfigurationUpdate(BaseModel):
    """更新配置请求"""

    value: Optional[str] = Field(None, description="配置值")


class ConfigurationResponse(BaseModel):
    """配置响应"""

    success: bool = Field(..., description="是否成功")
    data: Optional[Configuration] = Field(None, description="配置数据")
    message: Optional[str] = Field(None, description="消息")


class ConfigurationListResponse(BaseModel):
    """配置列表响应"""

    success: bool = Field(..., description="是否成功")
    data: Optional[list[Configuration]] = Field(None, description="配置列表")
    total: Optional[int] = Field(None, description="总数")
    message: Optional[str] = Field(None, description="消息")
