"""
通义千问大模型实现
"""

import os
import logging
from typing import List, Dict, Any, Optional

from .base import BaseLLM, LLMMessage, LLMResponse

logger = logging.getLogger(__name__)

try:
    from langchain_community.chat_models import ChatTongyi
    from langchain.schema import HumanMessage, SystemMessage, AIMessage
    LANGCHAIN_AVAILABLE = True
except ImportError:
    logger.warning("langchain相关包未安装，通义千问模型将不可用")
    LANGCHAIN_AVAILABLE = False


class TongyiLLM(BaseLLM):
    """通义千问大模型实现"""
    
    def __init__(self, 
                 api_key: Optional[str] = None,
                 model_name: str = "qwen-turbo",
                 temperature: float = 0.1,
                 max_tokens: int = 2000,
                 **kwargs):
        """
        初始化通义千问模型
        
        Args:
            api_key: API密钥，如果不提供则从环境变量获取
            model_name: 模型名称
            temperature: 温度参数
            max_tokens: 最大token数
            **kwargs: 其他参数
        """
        self.api_key = api_key or os.getenv("QWEN_API_KEY")
        self.model_name = model_name
        self.temperature = temperature
        self.max_tokens = max_tokens
        
        super().__init__(
            api_key=self.api_key,
            model_name=model_name,
            temperature=temperature,
            max_tokens=max_tokens,
            **kwargs
        )
    
    def _initialize(self):
        """初始化模型实例"""
        if not LANGCHAIN_AVAILABLE:
            raise RuntimeError("langchain相关包未安装，无法使用通义千问模型")
        
        if not self.api_key:
            raise ValueError("通义千问API密钥未配置，请设置QWEN_API_KEY环境变量")
        
        try:
            self.llm = ChatTongyi(
                dashscope_api_key=self.api_key,
                model_name=self.model_name,
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )
            logger.info(f"通义千问模型初始化成功: {self.model_name}")
        except Exception as e:
            logger.error(f"初始化通义千问模型失败: {str(e)}")
            raise
    
    def _convert_messages(self, messages: List[LLMMessage]) -> List:
        """将自定义消息格式转换为langchain格式"""
        langchain_messages = []
        
        for msg in messages:
            if msg.role == "user":
                langchain_messages.append(HumanMessage(content=msg.content))
            elif msg.role == "assistant":
                langchain_messages.append(AIMessage(content=msg.content))
            elif msg.role == "system":
                langchain_messages.append(SystemMessage(content=msg.content))
            else:
                # 默认作为用户消息处理
                langchain_messages.append(HumanMessage(content=msg.content))
        
        return langchain_messages
    
    def generate(self, messages: List[LLMMessage], **kwargs) -> LLMResponse:
        """
        生成文本
        
        Args:
            messages: 消息列表
            **kwargs: 生成参数
            
        Returns:
            LLMResponse: 生成结果
        """
        try:
            # 转换消息格式
            langchain_messages = self._convert_messages(messages)
            
            # 调用模型
            response = self.llm(langchain_messages)
            
            # 构建响应
            return LLMResponse(
                content=response.content,
                model=self.model_name,
                finish_reason="stop"
            )
            
        except Exception as e:
            logger.error(f"通义千问生成失败: {str(e)}")
            raise
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "provider": "阿里云",
            "model_name": self.model_name,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "api_key_configured": bool(self.api_key)
        }
    
    def is_available(self) -> bool:
        """检查模型是否可用"""
        try:
            if not LANGCHAIN_AVAILABLE:
                return False
            
            if not self.api_key:
                return False
            
            # 尝试简单调用测试可用性
            test_messages = [LLMMessage(content="测试", role="user")]
            self.generate(test_messages)
            return True
            
        except Exception as e:
            logger.warning(f"通义千问模型不可用: {str(e)}")
            return False
