"""
大模型管理模块
提供统一的大模型接口和枚举管理
"""

from enum import Enum
from typing import Dict, Type, Optional
import logging

from .base import BaseLLM, LLMMessage, LLMResponse
from .tongyi_llm import TongyiLLM

logger = logging.getLogger(__name__)


class LLMType(Enum):
    """大模型类型枚举"""

    TONGYI = "tongyi"
    # 可以在这里添加更多模型类型
    # OPENAI = "openai"
    # CLAUDE = "claude"


class LLMFactory:
    """大模型工厂类"""

    # 模型类型到实现类的映射
    _MODEL_MAPPING: Dict[LLMType, Type[BaseLLM]] = {
        LLMType.TONGYI: TongyiLLM,
    }

    @classmethod
    def create_llm(cls, llm_type: LLMType, **kwargs) -> BaseLLM:
        """
        创建指定类型的大模型实例

        Args:
            llm_type: 大模型类型
            **kwargs: 模型初始化参数

        Returns:
            BaseLLM: 大模型实例

        Raises:
            ValueError: 不支持的模型类型
        """
        if llm_type not in cls._MODEL_MAPPING:
            raise ValueError(f"不支持的模型类型: {llm_type}")

        model_class = cls._MODEL_MAPPING[llm_type]
        try:
            return model_class(**kwargs)
        except Exception as e:
            logger.error(f"创建{llm_type.value}模型失败: {str(e)}")
            raise

    @classmethod
    def get_available_models(cls) -> list:
        """获取可用的模型类型列表"""
        return list(cls._MODEL_MAPPING.keys())

    @classmethod
    def register_model(cls, llm_type: LLMType, model_class: Type[BaseLLM]):
        """
        注册新的模型类型

        Args:
            llm_type: 模型类型
            model_class: 模型实现类
        """
        cls._MODEL_MAPPING[llm_type] = model_class
        logger.info(f"注册新模型类型: {llm_type.value}")


# 便捷函数
def get_llm(llm_type: LLMType = LLMType.TONGYI, **kwargs) -> BaseLLM:
    """
    获取大模型实例的便捷函数

    Args:
        llm_type: 模型类型，默认为通义千问
        **kwargs: 模型初始化参数

    Returns:
        BaseLLM: 大模型实例
    """
    return LLMFactory.create_llm(llm_type, **kwargs)


# 导出主要接口
__all__ = [
    "LLMType",
    "LLMFactory",
    "get_llm",
    "BaseLLM",
    "LLMMessage",
    "LLMResponse",
    "TongyiLLM",
]
