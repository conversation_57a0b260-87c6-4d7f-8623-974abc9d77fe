"""
大模型基础抽象类
定义统一的大模型接口
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class LLMMessage:
    """大模型消息类"""
    content: str
    role: str = "user"  # user, assistant, system


@dataclass
class LLMResponse:
    """大模型响应类"""
    content: str
    usage: Optional[Dict[str, Any]] = None
    model: Optional[str] = None
    finish_reason: Optional[str] = None


class BaseLLM(ABC):
    """大模型基础抽象类"""
    
    def __init__(self, **kwargs):
        """
        初始化大模型
        
        Args:
            **kwargs: 模型配置参数
        """
        self.config = kwargs
        self._initialize()
    
    @abstractmethod
    def _initialize(self):
        """初始化模型实例"""
        pass
    
    @abstractmethod
    def generate(self, messages: List[LLMMessage], **kwargs) -> LLMResponse:
        """
        生成文本
        
        Args:
            messages: 消息列表
            **kwargs: 生成参数
            
        Returns:
            LLMResponse: 生成结果
        """
        pass
    
    def chat(self, content: str, **kwargs) -> str:
        """
        简单对话接口
        
        Args:
            content: 用户输入内容
            **kwargs: 生成参数
            
        Returns:
            str: 模型回复内容
        """
        messages = [LLMMessage(content=content, role="user")]
        response = self.generate(messages, **kwargs)
        return response.content
    
    @abstractmethod
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """检查模型是否可用"""
        pass
    
    def __call__(self, messages: List[LLMMessage], **kwargs) -> LLMResponse:
        """使对象可调用"""
        return self.generate(messages, **kwargs)
