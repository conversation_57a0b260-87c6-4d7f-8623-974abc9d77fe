"""
数据库连接管理工具
"""
import os
import mysql.connector
from mysql.connector import pooling
from contextlib import contextmanager
from typing import Optional
import logging

logger = logging.getLogger(__name__)


class DatabaseManager:
    """数据库连接管理器"""

    def __init__(self):
        self.pool: Optional[pooling.MySQLConnectionPool] = None
        self._initialize_pool()

    def _initialize_pool(self):
        """初始化数据库连接池"""
        try:
            config = {
                'host': os.getenv('DB_HOST', 'localhost'),
                'port': int(os.getenv('DB_PORT', '3306')),
                'user': os.getenv('DB_USER', 'root'),
                'password': os.getenv('DB_PASSWORD', ''),
                'database': os.getenv('DB_NAME', 'translator'),
                'charset': 'utf8mb4',
                'collation': 'utf8mb4_unicode_ci',
                'autocommit': False,
                'pool_name': 'translator_pool',
                'pool_size': int(os.getenv('DB_POOL_SIZE', '10')),
                'pool_reset_session': True,
            }

            self.pool = pooling.MySQLConnectionPool(**config)
            logger.info("数据库连接池初始化成功")

        except Exception as e:
            logger.warning(f"数据库连接池初始化失败: {e}")
            logger.warning("API将在无数据库模式下运行")
            self.pool = None

    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        connection = None
        try:
            connection = self.pool.get_connection()
            yield connection
        except Exception as e:
            if connection:
                connection.rollback()
            logger.error(f"数据库操作错误: {e}")
            raise
        finally:
            if connection:
                connection.close()

    def execute_script(self, script: str):
        """执行SQL脚本"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            try:
                # 分割脚本为单独的语句
                statements = [stmt.strip() for stmt in script.split(';') if stmt.strip()]

                for statement in statements:
                    cursor.execute(statement)

                conn.commit()
                logger.info("SQL脚本执行成功")

            except Exception as e:
                conn.rollback()
                logger.error(f"SQL脚本执行失败: {e}")
                raise
            finally:
                cursor.close()

    def create_tables(self):
        """创建数据库表结构"""
        create_tables_sql = """
        CREATE TABLE IF NOT EXISTS translation_tasks (
            id VARCHAR(36) PRIMARY KEY,
            status VARCHAR(20) NOT NULL DEFAULT 'processing',
            total_records INTEGER NOT NULL,
            completed_records INTEGER DEFAULT 0,
            failed_records INTEGER DEFAULT 0,
            config JSON NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_status (status),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

        CREATE TABLE IF NOT EXISTS translation_records (
            id INTEGER PRIMARY KEY AUTO_INCREMENT,
            task_id VARCHAR(36) NOT NULL,
            source_id INTEGER NOT NULL,
            source_text TEXT NOT NULL,
            target_language VARCHAR(10) NOT NULL,
            translated_text TEXT,
            status VARCHAR(20) DEFAULT 'pending',
            error_message TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (task_id) REFERENCES translation_tasks(id) ON DELETE CASCADE,
            INDEX idx_task_source (task_id, source_id),
            INDEX idx_status (status),
            INDEX idx_task_status (task_id, status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """

        self.execute_script(create_tables_sql)

    def check_connection(self) -> bool:
        """检查数据库连接是否正常"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
                cursor.close()
                return True
        except Exception as e:
            logger.error(f"数据库连接检查失败: {e}")
            return False


# 全局数据库管理器实例
db_manager = DatabaseManager()


def get_db_connection():
    """获取数据库连接（用于API层）"""
    if db_manager.pool is None:
        raise Exception("数据库连接池未初始化，无法获取数据库连接")
    return db_manager.pool.get_connection()