"""
配置服务
"""

import logging
from typing import Optional, List, Dict, Any
from app.config.postgresql import get_db_connection
from app.models.configuration import (
    Configuration,
    ConfigurationCreate,
    ConfigurationUpdate,
)

logger = logging.getLogger(__name__)


class ConfigurationService:
    """配置服务类"""

    def __init__(self):
        """初始化配置服务"""
        self.connection = None

    def _get_connection(self):
        """获取数据库连接"""
        if not self.connection:
            self.connection = get_db_connection()
        return self.connection

    def get_config(self, key: str) -> Optional[Configuration]:
        """
        根据key获取配置

        Args:
            key: 配置键名

        Returns:
            Configuration: 配置对象，如果不存在返回None
        """
        try:
            conn = self._get_connection()
            if not conn:
                logger.error("数据库连接失败")
                return None

            with conn.cursor() as cursor:
                cursor.execute(
                    "SELECT id, key, value, create_time, update_time FROM configuration WHERE key = %s",
                    (key,),
                )
                row = cursor.fetchone()

                if row:
                    return Configuration(
                        id=row[0],
                        key=row[1],
                        value=row[2],
                        create_time=row[3],
                        update_time=row[4],
                    )
                return None

        except Exception as e:
            logger.error(f"获取配置失败: {str(e)}")
            return None

    def get_all_configs(self) -> List[Configuration]:
        """
        获取所有配置

        Returns:
            List[Configuration]: 配置列表
        """
        try:
            conn = self._get_connection()
            if not conn:
                logger.error("数据库连接失败")
                return []

            with conn.cursor() as cursor:
                cursor.execute(
                    "SELECT id, key, value, create_time, update_time FROM configuration ORDER BY key"
                )
                rows = cursor.fetchall()

                configs = []
                for row in rows:
                    configs.append(
                        Configuration(
                            id=row[0],
                            key=row[1],
                            value=row[2],
                            create_time=row[3],
                            update_time=row[4],
                        )
                    )
                return configs

        except Exception as e:
            logger.error(f"获取所有配置失败: {str(e)}")
            return []

    def create_config(
        self, config_data: ConfigurationCreate
    ) -> Optional[Configuration]:
        """
        创建配置

        Args:
            config_data: 配置创建数据

        Returns:
            Configuration: 创建的配置对象
        """
        try:
            conn = self._get_connection()
            if not conn:
                logger.error("数据库连接失败")
                return None

            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    INSERT INTO configuration (key, value)
                    VALUES (%s, %s)
                    RETURNING id, key, value, create_time, update_time
                    """,
                    (config_data.key, config_data.value),
                )
                row = cursor.fetchone()
                conn.commit()

                if row:
                    logger.info(f"创建配置成功: {config_data.key}")
                    return Configuration(
                        id=row[0],
                        key=row[1],
                        value=row[2],
                        create_time=row[3],
                        update_time=row[4],
                    )
                return None

        except Exception as e:
            logger.error(f"创建配置失败: {str(e)}")
            if conn:
                conn.rollback()
            return None

    def update_config(
        self, key: str, config_data: ConfigurationUpdate
    ) -> Optional[Configuration]:
        """
        更新配置

        Args:
            key: 配置键名
            config_data: 配置更新数据

        Returns:
            Configuration: 更新后的配置对象
        """
        try:
            conn = self._get_connection()
            if not conn:
                logger.error("数据库连接失败")
                return None

            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    UPDATE configuration
                    SET value = %s, update_time = CURRENT_TIMESTAMP
                    WHERE key = %s
                    RETURNING id, key, value, create_time, update_time
                    """,
                    (config_data.value, key),
                )
                row = cursor.fetchone()
                conn.commit()

                if row:
                    logger.info(f"更新配置成功: {key}")
                    return Configuration(
                        id=row[0],
                        key=row[1],
                        value=row[2],
                        create_time=row[3],
                        update_time=row[4],
                    )
                return None

        except Exception as e:
            logger.error(f"更新配置失败: {str(e)}")
            if conn:
                conn.rollback()
            return None

    def delete_config(self, key: str) -> bool:
        """
        删除配置

        Args:
            key: 配置键名

        Returns:
            bool: 是否删除成功
        """
        try:
            conn = self._get_connection()
            if not conn:
                logger.error("数据库连接失败")
                return False

            with conn.cursor() as cursor:
                cursor.execute("DELETE FROM configuration WHERE key = %s", (key,))
                affected_rows = cursor.rowcount
                conn.commit()

                if affected_rows > 0:
                    logger.info(f"删除配置成功: {key}")
                    return True
                return False

        except Exception as e:
            logger.error(f"删除配置失败: {str(e)}")
            if conn:
                conn.rollback()
            return False

    def get_config_value(
        self, key: str, default: Optional[str] = None
    ) -> Optional[str]:
        """
        获取配置值的便捷方法

        Args:
            key: 配置键名
            default: 默认值

        Returns:
            str: 配置值
        """
        config = self.get_config(key)
        if config and config.value is not None:
            return config.value
        return default

    def set_config_value(self, key: str, value: str) -> bool:
        """
        设置配置值的便捷方法

        Args:
            key: 配置键名
            value: 配置值

        Returns:
            bool: 是否设置成功
        """
        # 先尝试更新
        updated_config = self.update_config(key, ConfigurationUpdate(value=value))
        if updated_config:
            return True

        # 如果更新失败，尝试创建
        created_config = self.create_config(ConfigurationCreate(key=key, value=value))
        return created_config is not None
