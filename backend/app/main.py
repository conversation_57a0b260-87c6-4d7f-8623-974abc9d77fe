"""
Flask应用入口文件
"""

import logging
from flask import Flask, jsonify
from flask_cors import CORS
from dotenv import load_dotenv

from app.config.settings import settings
from app.api.translate import translate_bp

# 加载环境变量
load_dotenv()


def create_app() -> Flask:
    """创建Flask应用"""
    app = Flask(__name__)

    # 基础配置
    app.config["SECRET_KEY"] = settings.SECRET_KEY
    app.config["DEBUG"] = settings.DEBUG

    # 配置日志
    if not app.debug:
        logging.basicConfig(level=logging.INFO)

    # 配置CORS
    CORS(
        app,
        origins=settings.cors_origins_list,
        methods=["GET", "POST", "OPTIONS"],
        allow_headers=[
            "Content-Type",
            "Authorization",
            "X-Requested-With",
            "Accept",
            "Origin",
        ],
        supports_credentials=True,
    )

    # 注册蓝图
    app.register_blueprint(translate_bp, url_prefix="/api")



    # 全局OPTIONS请求处理器
    @app.before_request
    def handle_preflight():
        from flask import request, make_response

        if request.method == "OPTIONS":
            response = make_response()
            response.headers.add("Access-Control-Allow-Origin", "*")
            response.headers.add("Access-Control-Allow-Headers", "*")
            response.headers.add("Access-Control-Allow-Methods", "*")
            return response

    # 全局错误处理
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({
            "success": False,
            "error": "接口不存在"
        }), 404

    @app.errorhandler(500)
    def internal_error(error):
        return jsonify({
            "success": False,
            "error": "服务器内部错误"
        }), 500

    return app


def main():
    """主函数"""
    app = create_app()

    print(f"Starting translation service on {settings.HOST}:{settings.PORT}")
    print(f"Debug mode: {settings.DEBUG}")
    print(f"CORS origins: {settings.cors_origins_list}")

    # 启动应用
    app.run(
        host=settings.HOST,
        port=settings.PORT,
        debug=settings.DEBUG,
    )


if __name__ == "__main__":
    main()
