<template>
  <div class="max-w-4xl mx-auto">
    <div class="text-center mb-8">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">翻译配置</h2>
      <p class="text-lg text-gray-600">选择需要翻译的语言和翻译选项</p>
    </div>

    <div class="space-y-8">
      <!-- 源语言选择 -->
      <div class="bg-white rounded-2xl shadow-xl border border-gray-100 p-8">
        <div class="flex items-center space-x-3 mb-6">
          <div
            class="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center"
          >
            <font-awesome-icon icon="language" class="text-blue-600" />
          </div>
          <h3 class="text-xl font-semibold text-gray-900">源语言</h3>
          <div class="text-sm text-gray-500">（仅支持英语作为源语言）</div>
        </div>

        <!-- 检查是否有en-US -->
        <div
          v-if="hasSourceLanguage"
          class="flex items-center space-x-4 p-6 bg-blue-50 rounded-xl border border-blue-200"
        >
          <div
            class="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center"
          >
            <font-awesome-icon icon="check-circle" class="text-white text-xl" />
          </div>
          <div>
            <h4 class="text-lg font-semibold text-blue-900">源语言已确认</h4>
            <p class="text-blue-700">
              检测到 (<span
                v-for="(lang, index) in global.includeSourceLangList"
                :key="lang"
                >{{ lang
                }}{{
                  index === global.includeSourceLangList.length - 1 ? '' : '、'
                }}</span
              >)，可以进行翻译
            </p>
          </div>
        </div>

        <!-- 没有en-US的错误提示 -->
        <div
          v-else
          class="flex items-center space-x-4 p-6 bg-red-50 rounded-xl border border-red-200"
        >
          <div
            class="w-12 h-12 bg-red-600 rounded-xl flex items-center justify-center"
          >
            <font-awesome-icon
              icon="exclamation-triangle"
              class="text-white text-xl"
            />
          </div>
          <div>
            <h4 class="text-lg font-semibold text-red-900">缺少源语言内容</h4>
            <p class="text-red-700 mb-2">
              未检测到英语 (en-US) 内容，无法进行翻译
            </p>
            <p class="text-sm text-red-600">请确保Excel文件中包含 "en-US" 列</p>
          </div>
        </div>

        <!-- 显示检测到的可翻译语言列表 -->
        <div class="mt-6">
          <h5 class="text-sm font-medium text-gray-700 mb-3">
            显示检测到的可翻译语言列表：
          </h5>
          <div class="flex flex-wrap gap-2">
            <span
              v-for="language in languages.filter((language) =>
                global.includeSourceLangList.includes(language)
              )"
              :key="language"
              :class="[
                'px-3 py-1 rounded-full text-sm font-medium',
                language === 'en-US'
                  ? 'bg-blue-100 text-blue-800 border border-blue-200'
                  : 'bg-gray-100 text-gray-600 border border-gray-200',
              ]"
            >
              {{ language }}
              <font-awesome-icon
                v-if="language === global.includeSourceLangList[0]"
                icon="check"
                class="ml-1 text-blue-600"
              />
            </span>
          </div>
        </div>
      </div>

      <!-- 目标语言选择 -->
      <div class="bg-white rounded-2xl shadow-xl border border-gray-100 p-8">
        <div class="flex items-center justify-between mb-6">
          <div class="flex items-center space-x-3">
            <div
              class="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center"
            >
              <font-awesome-icon icon="globe" class="text-green-600" />
            </div>
            <h3 class="text-xl font-semibold text-gray-900">目标语言</h3>
          </div>

          <div class="flex items-center space-x-2">
            <button
              @click="selectAllTargets"
              class="px-3 py-1 text-sm text-blue-600 hover:text-blue-700 font-medium"
            >
              全选
            </button>
            <span class="text-gray-300">|</span>
            <button
              @click="clearAllTargets"
              class="px-3 py-1 text-sm text-gray-600 hover:text-gray-700 font-medium"
            >
              清空
            </button>
          </div>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          <label
            v-for="language in availableTargetLanguages"
            :key="language"
            class="relative cursor-pointer"
          >
            <input
              v-model="config.targetLanguages"
              :value="language"
              type="checkbox"
              class="sr-only"
            />
            <div
              :class="[
                'flex items-center space-x-3 p-4 rounded-lg border-2 transition-all duration-200',
                config.targetLanguages.includes(language)
                  ? 'border-green-500 bg-green-50 text-green-700'
                  : 'border-gray-200 hover:border-gray-300 text-gray-700',
              ]"
            >
              <div
                :class="[
                  'w-4 h-4 rounded border-2 flex items-center justify-center',
                  config.targetLanguages.includes(language)
                    ? 'border-green-500 bg-green-500'
                    : 'border-gray-300',
                ]"
              >
                <font-awesome-icon
                  v-if="config.targetLanguages.includes(language)"
                  icon="check"
                  class="text-white text-xs"
                />
              </div>
              <span class="font-medium">{{ language }}</span>
            </div>
          </label>
        </div>
      </div>

      <!-- 翻译选项 -->
      <div class="bg-white rounded-2xl shadow-xl border border-gray-100 p-8">
        <div class="flex items-center space-x-3 mb-6">
          <div
            class="w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center"
          >
            <font-awesome-icon icon="cog" class="text-purple-600" />
          </div>
          <h3 class="text-xl font-semibold text-gray-900">翻译选项</h3>
        </div>

        <div class="space-y-4">
          <label class="flex items-center space-x-3 cursor-pointer">
            <input
              v-model="config.overwriteExisting"
              type="checkbox"
              class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            />
            <span class="text-gray-700">覆盖已有翻译</span>
            <div class="text-sm text-gray-500">
              （如果目标语言已有内容，是否覆盖）
            </div>
          </label>

          <label class="flex items-center space-x-3 cursor-pointer">
            <input
              v-model="config.skipEmpty"
              type="checkbox"
              class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            />
            <span class="text-gray-700">跳过空白条目</span>
            <div class="text-sm text-gray-500">
              （源语言为空的条目不进行翻译）
            </div>
          </label>

          <label class="flex items-center space-x-3 cursor-pointer">
            <input
              v-model="config.preserveFormatting"
              type="checkbox"
              class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            />
            <span class="text-gray-700">保持格式</span>
            <div class="text-sm text-gray-500">
              （保持原文的换行、空格等格式）
            </div>
          </label>
        </div>
      </div>

      <!-- 配置摘要 -->
      <div
        class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 border border-blue-100"
      >
        <h4 class="text-lg font-semibold text-gray-900 mb-4">配置摘要</h4>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="text-center">
            <div class="text-2xl font-bold text-blue-600 mb-1">
              {{ hasSourceLanguage ? 'en-US' : '-' }}
            </div>
            <div class="text-sm text-gray-600">源语言</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-green-600 mb-1">
              {{ config.targetLanguages.length }}
            </div>
            <div class="text-sm text-gray-600">目标语言</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-purple-600 mb-1">
              {{ Object.values(config).filter((v) => v === true).length }}
            </div>
            <div class="text-sm text-gray-600">启用选项</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex items-center justify-between mt-8">
      <button
        @click="$emit('back')"
        class="inline-flex items-center px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium rounded-lg transition-colors duration-200"
      >
        <font-awesome-icon icon="arrow-left" class="mr-2" />
        上一步
      </button>

      <button
        @click="startTranslation"
        :disabled="!canStartTranslation"
        :class="[
          'inline-flex items-center px-8 py-3 font-medium rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl',
          canStartTranslation
            ? 'bg-green-600 hover:bg-green-700 text-white'
            : 'bg-gray-300 text-gray-500 cursor-not-allowed',
        ]"
      >
        <font-awesome-icon icon="play" class="mr-2" />
        开始翻译
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { TranslationConfig } from '@/types/translate.types';
import global from '@/utils/global.helper';
import { ref, computed, watchEffect } from 'vue';

const props = defineProps<{
  languages: string[];
}>();

const emit = defineEmits<{
  startTranslation: [config: any];
  back: [];
}>();

// 翻译配置
const config = ref({
  sourceLanguage: '',
  targetLanguages: [] as string[],
  overwriteExisting: false,
  skipEmpty: true,
  preserveFormatting: true,
});

// 计算属性
const hasSourceLanguage = computed(() => {
  return global.includeSourceLangList.some((lang) =>
    props.languages.includes(lang)
  );
});

const availableTargetLanguages = computed(() => {
  return props.languages.filter(
    (lang) => !global.excludeTargetLangList.includes(lang)
  );
});

const canStartTranslation = computed(() => {
  return hasSourceLanguage.value && config.value.targetLanguages.length > 0;
});

// 方法
const selectAllTargets = () => {
  config.value.targetLanguages = [...availableTargetLanguages.value];
};

const clearAllTargets = () => {
  config.value.targetLanguages = [];
};

const startTranslation = () => {
  if (canStartTranslation.value) {
    // 构建翻译配置数据
    const translationConfig: TranslationConfig = {
      sourceLanguage: config.value.sourceLanguage,
      targetLanguages: config.value.targetLanguages,
      overwriteExisting: config.value.overwriteExisting,
      skipEmpty: config.value.skipEmpty,
      preserveFormatting: config.value.preserveFormatting,
    };

    // 构建符合新格式的翻译请求数据
    // 注释: 按照新的JSON格式构建翻译请求
    const translationRequest = {
      config: {
        overwriteExisting: config.value.overwriteExisting,
        skipEmpty: config.value.skipEmpty,
        preserveFormatting: config.value.preserveFormatting,
        batchSize: 10, // 默认批处理大小
      },
      // 注释: 这里只传递配置信息，实际的batches数据将在Home.vue中构建
      batches: [],
    };

    console.log('开始翻译，配置:', translationConfig);
    console.log('翻译请求格式:', translationRequest);

    // 同时传递原始配置和新格式的请求数据
    emit('startTranslation', {
      originalConfig: translationConfig,
      translationRequest: translationRequest,
    });
  }
};

// 监听 props.languages 变化，自动初始化配置
watchEffect(() => {
  // 只有当 languages 有数据时才初始化
  if (props.languages.length > 0) {
    // 设置源语言
    if (hasSourceLanguage.value && !config.value.sourceLanguage) {
      config.value.sourceLanguage =
        global.includeSourceLangList.find((lang) =>
          props.languages.includes(lang)
        ) || '';
    }

    // 目标语言默认全选（只在初始化时设置，避免重复设置）
    if (
      config.value.targetLanguages.length === 0 &&
      availableTargetLanguages.value.length > 0
    ) {
      config.value.targetLanguages = [...availableTargetLanguages.value];
    }
  }
});
</script>
