/**
 * 页面UI相关的类型定义
 * 与API接口类型分离，专门用于页面组件
 */

// ============= 页面状态类型 =============
export type UploadStatus =
  | 'idle'
  | 'uploading'
  | 'processing'
  | 'completed'
  | 'error';

// ============= 页面数据类型 =============
export interface ExcelData {
  languages: string[];
  translations: Record<string, Record<string, string>>;
  metadata: {
    filename: string;
    total_keys: number;
    total_languages: number;
    sheet_name: string;
  };
}

// ============= 组件交互类型 =============
export interface ScrollPosition {
  x: number;
  y: number;
}

export interface TranslationItem {
  key: string;
  text: string;
}

// ============= 历史记录页面类型 =============
export interface HistoryTask {
  taskId: string;
  originalFilename: string;
  status: 'processing' | 'completed' | 'failed';
  progress: number;
  message: string;
  createdTime: string;
  downloadUrl?: string;
  description?: string;
}

// 重新导出API类型以保持兼容性
export type { TranslationTask } from '@/api/translate/types';

// ============= 进度更新类型 =============
export interface ProgressUpdate {
  taskId: string;
  progress: number;
  message: string;
  status?: string;
}
